@echo off
setlocal enabledelayedexpansion
echo ===================================================
echo Android SDK and Emulator Setup Script
echo ===================================================
echo.

REM Try to find Android SDK in common locations
echo Searching for Android SDK in common locations...
set "ANDROID_SDK_LOCATIONS=C:\Users\<USER>\AppData\Local\Android\Sdk C:\Program Files\Android\Sdk C:\Program Files (x86)\Android\Sdk %LOCALAPPDATA%\Android\Sdk %USERPROFILE%\Library\Android\sdk"

for %%i in (%ANDROID_SDK_LOCATIONS%) do (
    if exist "%%i" (
        echo Found Android SDK at: %%i
        set ANDROID_HOME=%%i
        goto :found
    )
)

echo Android SDK not found in common locations.
echo Please enter the full path to your Android SDK:
set /p ANDROID_HOME=

:found
echo Using Android SDK at: %ANDROID_HOME%
echo.

REM Create local.properties file
echo Creating local.properties file...
if not exist android mkdir android
echo sdk.dir=%ANDROID_HOME% > android\local.properties
echo Created android\local.properties with SDK path
echo.

REM Check if Android SDK tools are installed
echo Checking for required SDK components...
set SDKMANAGER=%ANDROID_HOME%\cmdline-tools\latest\bin\sdkmanager.bat
set AVDMANAGER=%ANDROID_HOME%\cmdline-tools\latest\bin\avdmanager.bat

if not exist "%SDKMANAGER%" (
    echo SDK Manager not found at: %SDKMANAGER%
    echo Looking for alternative locations...

    for /d %%d in ("%ANDROID_HOME%\cmdline-tools\*") do (
        if exist "%%d\bin\sdkmanager.bat" (
            set SDKMANAGER=%%d\bin\sdkmanager.bat
            set AVDMANAGER=%%d\bin\avdmanager.bat
            echo Found SDK Manager at: !SDKMANAGER!
            goto :sdkmanager_found
        )
    )

    echo SDK Manager not found. Installing command-line tools...
    echo Please download Android command-line tools from:
    echo https://developer.android.com/studio#command-tools
    echo.
    echo After downloading, extract the tools and place them in:
    echo %ANDROID_HOME%\cmdline-tools\latest\
    echo.
    echo Then run this script again.
    goto :end
)

:sdkmanager_found
echo SDK Manager found at: %SDKMANAGER%
echo.

REM Install required packages
echo Installing required SDK packages...
echo This may take some time. Please be patient.
echo.
echo y | call "%SDKMANAGER%" "platform-tools" "platforms;android-34" "build-tools;34.0.0" "system-images;android-34;google_apis;x86_64" "emulator"
echo.
echo SDK packages installation complete.
echo.

REM Check if emulator exists
if exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo Found emulator at: %ANDROID_HOME%\emulator\emulator.exe
    echo.

    echo Listing available Android Virtual Devices (AVDs):
    "%ANDROID_HOME%\emulator\emulator.exe" -list-avds
    echo.

    set /p CREATE_AVD=No AVDs found or want to create a new one? (y/n):
    if /i "%CREATE_AVD%"=="y" (
        echo.
        echo Creating a new Android Virtual Device (AVD)...
        set /p AVD_NAME=Enter a name for your AVD (e.g., Pixel_API_34):

        echo.
        echo Creating AVD named %AVD_NAME%...
        call "%AVDMANAGER%" create avd -n %AVD_NAME% -k "system-images;android-34;google_apis;x86_64" -d "pixel_5"

        echo.
        echo AVD created successfully!
        echo.
        echo You can now start your emulator with:
        echo "%ANDROID_HOME%\emulator\emulator.exe" -avd %AVD_NAME%
        echo.

        set /p START_EMU=Do you want to start the emulator now? (y/n):
        if /i "%START_EMU%"=="y" (
            echo Starting emulator...
            start "" "%ANDROID_HOME%\emulator\emulator.exe" -avd %AVD_NAME%
            echo Emulator is starting in a new window.
        )
    )
) else (
    echo Emulator not found at: %ANDROID_HOME%\emulator\emulator.exe
    echo Installing emulator package...
    echo y | call "%SDKMANAGER%" "emulator"
    echo.
    echo Please run this script again after installation completes.
)

:end
echo.
echo ===================================================
echo Setup complete!
echo.
echo You can now run "npm run android" to start your app.
echo If you encounter any issues, make sure your emulator is running.
echo ===================================================
