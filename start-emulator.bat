@echo off
echo Starting Android Emulator...

REM Find Android SDK
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
) else (
    echo Android SDK not found. Please run quick-android-setup.bat first.
    pause
    exit /b 1
)

REM Check if emulator exists
if not exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo Emulator not found. Please install Android Studio.
    pause
    exit /b 1
)

REM List available AVDs
echo Available AVDs:
"%ANDROID_HOME%\emulator\emulator.exe" -list-avds

REM Start the Pixel_9_Pro emulator (since we know it exists)
echo.
echo Starting Pixel_9_Pro emulator...
start "" "%ANDROID_HOME%\emulator\emulator.exe" -avd Pixel_9_Pro

echo Emulator is starting in a new window.
echo Once it's fully loaded, you can run: npm run android
pause
